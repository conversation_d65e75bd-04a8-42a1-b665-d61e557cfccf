/// Firebase App Check 환경 설정
/// 
/// 개발/프로덕션 환경에 따른 App Check 설정을 관리합니다.
library;

import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:firebase_app_check/firebase_app_check.dart';

class AppCheckConfig {
  // 환경 설정 - Flutter의 빌드 모드에 따라 자동 결정
  static bool get isDebugMode => kDebugMode; // 디버그 모드는 kDebugMode로 자동 감지

  /// App Check 활성화 여부
  /// 권장: 프로덕션(릴리즈)에서만 기본 활성화. 개발/디버그에서는 비활성화하여 개발 편의성 확보.
  static bool get isAppCheckEnabled {
    return !kDebugMode; // 디버그=false, 릴리즈/프로파일=true
  }

  // 주의: 디버그 토큰은 하드코딩하지 않습니다.
  // Firebase가 자동으로 생성한 실제 토큰을 로그에서 확인하고 Firebase 콘솔에 등록하세요.
  
  /// 현재 환경에 맞는 Android Provider 반환
  static AndroidProvider get androidProvider {
    return isDebugMode 
        ? AndroidProvider.debug 
        : AndroidProvider.playIntegrity;
  }
  
  /// 현재 환경에 맞는 Apple Provider 반환
  /// iOS 14.0+ 지원으로 App Attest 사용 (더 강력한 보안)
  static AppleProvider get appleProvider {
    return isDebugMode
        ? AppleProvider.debug
        : AppleProvider.appAttest; // iOS 14.0+에서 지원하는 최신 보안 기술
  }

  /// 웹용 reCAPTCHA v2 사이트 키 (전화번호 인증용 - 체크박스)
  /// 기존 시스템 유지 - 사람인지 확인하는 중요한 기능
  static String get webRecaptchaV2SiteKey {
    // 기존 v2 사이트 키 (전화번호 인증용)
    return '6LdfCK4rAAAAAFjdN1h44yAUhSj4aLeklB3U1DO_';
  }

  /// 웹용 reCAPTCHA v3 사이트 키 (백그라운드 보안용)
  /// 페이지 진입 시 백그라운드에서 실행
  static String get webRecaptchaV3SiteKey {
    // 새로운 v3 사이트 키 (백그라운드 보안)
    return '6LcfOK8rAAAAAPcKb5ByuyWQVHPaJXcGLGRCRs0S';
  }

  /// 웹용 reCAPTCHA v3 시크릿 키 (서버에서만 사용)
  /// 참고: 실제로는 Firebase Functions에서 환경변수로 관리
  static String get webRecaptchaV3SecretKey {
    return '6LcfOK8rAAAAAE30usCP1onSES4kj2fZ7xSXSfEz';
  }

  /// 기본 웹 reCAPTCHA 키 (하위 호환성)
  static String get webRecaptchaSiteKey => webRecaptchaV2SiteKey;
  
  /// 디버그 토큰 안내 메시지 반환
  static String get currentDebugToken {
    if (Platform.isAndroid) {
      return 'Android 디버그 토큰이 로그에 자동 출력됩니다. 로그에서 "Firebase App Check debug token" 를 찾아보세요.';
    } else if (Platform.isIOS) {
      return 'iOS 디버그 토큰이 로그에 자동 출력됩니다. 로그에서 "Firebase App Check debug token" 를 찾아보세요.';
    } else {
      return 'Unknown Platform';
    }
  }
  
  /// 현재 설정 정보 출력용
  static Map<String, dynamic> get configInfo {
    return {
      'isDebugMode': isDebugMode,
      'platform': Platform.isAndroid ? 'Android' : Platform.isIOS ? 'iOS' : 'Unknown',
      'androidProvider': isDebugMode ? 'Debug' : 'Play Integrity',
      'appleProvider': isDebugMode ? 'Debug' : 'App Attest (iOS 14.0+ 최신 보안)',
      'webSupport': 'reCAPTCHA v2+v3 병행 (v2: ${webRecaptchaV2SiteKey.substring(0, 20)}..., v3: ${webRecaptchaV3SiteKey.substring(0, 20)}...)',
      'appCheckStatus': isAppCheckEnabled ? '활성화됨 🔒' : '비활성화됨 🔧 (개발 모드)',
      'productionNote': isAppCheckEnabled ? '프로덕션 모드' : '⚠️ 프로덕션 배포 전 isAppCheckEnabled를 true로 변경하세요',
      'debugToken': isDebugMode ? '로그에서 자동 생성된 토큰을 확인하세요' : 'N/A (Production)',
    };
  }
}
